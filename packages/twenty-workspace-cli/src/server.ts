import { NestFactory } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import { useContainer } from 'class-validator';

import { WorkspaceCliSimpleModule } from './workspace-cli-simple.module';

const bootstrap = async () => {
  const app = await NestFactory.create<NestExpressApplication>(
    WorkspaceCliSimpleModule,
    {
      cors: true,
      bufferLogs: false,
    },
  );

  // Apply class-validator container so that we can use injection in validators
  useContainer(app.select(WorkspaceCliSimpleModule), {
    fallbackOnErrors: true,
  });

  // Configure JSON body parsing
  app.useBodyParser('json', { limit: '10mb' });
  app.useBodyParser('urlencoded', {
    limit: '10mb',
    extended: true,
  });

  const port = process.env.PORT || 3002;

  console.log(`🚀 Twenty Workspace CLI API starting on port ${port}`);
  console.log(`📋 Available endpoints:`);
  console.log(`   GET    /health - Health check`);
  console.log(`   GET    /workspaces - List workspaces`);
  console.log(`   POST   /workspaces - Create workspace`);
  console.log(`   PUT    /workspaces/:id - Update workspace`);
  console.log(`   DELETE /workspaces/:id - Delete workspace`);
  console.log(`   GET    /workspaces/:id/health - Check workspace health`);

  await app.listen(port);
};

bootstrap().catch((error) => {
  console.error('❌ Failed to start Twenty Workspace CLI API:', error);
  process.exit(1);
});
