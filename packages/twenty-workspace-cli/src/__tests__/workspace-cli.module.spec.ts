import { Test, TestingModule } from '@nestjs/testing';
import { WorkspaceCliModule } from '../workspace-cli.module';
import { WorkspaceService } from 'twenty-server/engine/core-modules/workspace/services/workspace.service';
import { CommandLogger } from '../utils/command-logger';

describe('WorkspaceCliModule', () => {
  let module: TestingModule;

  beforeEach(async () => {
    // Mock external dependencies that would normally come from twenty-server
    const mockWorkspaceService = {
      find: jest.fn(),
      findOneBy: jest.fn(),
      deleteWorkspace: jest.fn(),
      updateWorkspaceById: jest.fn(),
    };

    const mockTwentyConfigService = {
      get: jest.fn().mockReturnValue('mock-value'),
    };

    const mockLoggerService = {
      log: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
    };

    const mockExceptionHandlerService = {
      captureExceptions: jest.fn(),
    };

    const mockDomainManagerService = {
      isCustomDomainWorking: jest.fn(),
    };

    module = await Test.createTestingModule({
      imports: [WorkspaceCliModule],
    })
      .overrideProvider(WorkspaceService)
      .useValue(mockWorkspaceService)
      .overrideProvider('TwentyConfigService')
      .useValue(mockTwentyConfigService)
      .overrideProvider('LoggerService')
      .useValue(mockLoggerService)
      .overrideProvider('ExceptionHandlerService')
      .useValue(mockExceptionHandlerService)
      .overrideProvider('DomainManagerService')
      .useValue(mockDomainManagerService)
      .compile();
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
  });

  it('should be defined', () => {
    expect(module).toBeDefined();
  });

  it('should provide WorkspaceService', () => {
    const workspaceService = module.get<WorkspaceService>(WorkspaceService);
    expect(workspaceService).toBeDefined();
  });

  it('should provide CommandLogger', () => {
    const commandLogger = module.get<CommandLogger>(CommandLogger);
    expect(commandLogger).toBeDefined();
  });

  it('should provide all command classes', () => {
    const commands = [
      'WorkspaceListCommand',
      'WorkspaceHealthCommand',
      'WorkspaceUpdateCommand',
      'WorkspaceCreateCommand',
      'WorkspaceDeleteCommand',
    ];

    commands.forEach((commandName) => {
      expect(() => module.get(commandName)).not.toThrow();
    });
  });
});
