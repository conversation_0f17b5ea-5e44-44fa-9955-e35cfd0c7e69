export interface WorkspaceListOptions {
  status?: 'active' | 'inactive' | 'pending' | 'all';
  limit?: number;
  offset?: number;
  subdomain?: string;
  customDomain?: string;
  format?: 'table' | 'json' | 'csv';
  verbose?: boolean;
}

export interface WorkspaceHealthOptions {
  workspaceId: string;
  fix?: boolean;
  dryRun?: boolean;
  checkDomain?: boolean;
  checkMetadata?: boolean;
  checkUsers?: boolean;
}

export interface WorkspaceUpdateOptions {
  workspaceId: string;
  displayName?: string;
  subdomain?: string;
  customDomain?: string;
  allowImpersonation?: boolean;
  isPublicInviteLinkEnabled?: boolean;
  isGoogleAuthEnabled?: boolean;
  isMicrosoftAuthEnabled?: boolean;
  isPasswordAuthEnabled?: boolean;
  dryRun?: boolean;
}

export interface WorkspaceCreateOptions {
  displayName: string;
  subdomain: string;
  customDomain?: string;
  ownerEmail?: string;
  activate?: boolean;
}

export interface WorkspaceDeleteOptions {
  workspaceId: string;
  soft?: boolean;
  hard?: boolean;
  force?: boolean;
  confirm?: boolean;
}

export interface WorkspaceBulkOperationOptions {
  operation: 'update' | 'suspend' | 'activate' | 'delete';
  filter?: {
    status?: string;
    createdBefore?: Date;
    lastActiveBefore?: Date;
    customDomain?: boolean;
  };
  batchSize?: number;
  dryRun?: boolean;
  confirm?: boolean;
}
