#!/usr/bin/env node

import 'reflect-metadata';
import { CommandFactory } from 'nest-commander';
import { WorkspaceCliModule } from './workspace-cli.module';
import { LoggerService } from 'twenty-server/engine/core-modules/logger/logger.service';
import { ExceptionHandlerService } from 'twenty-server/engine/core-modules/exception-handler/exception-handler.service';
import { shouldCaptureException } from 'twenty-server/engine/utils/global-exception-handler.util';

async function bootstrap() {
  const app = await CommandFactory.createWithoutRunning(WorkspaceCliModule, {
    logger: ['error', 'warn', 'log'],
    bufferLogs: process.env.LOGGER_IS_BUFFER_ENABLED === 'true',
  });

  const loggerService = app.get(LoggerService);
  const exceptionHandlerService = app.get(ExceptionHandlerService);

  const errorHandler = (err: Error) => {
    loggerService.error(err?.message, err?.name);

    if (shouldCaptureException(err)) {
      exceptionHandlerService.captureExceptions([err]);
    }
  };

  // Inject our logger
  app.useLogger(loggerService);

  // Display CLI header
  console.log(`
╔══════════════════════════════════════════════════════╗
║                                                      ║
║    Twenty Workspace CLI - Administrative Tool       ║
║    Advanced workspace management and operations     ║
║                                                      ║
╚══════════════════════════════════════════════════════╝
  `);

  await CommandFactory.runApplication(app);

  app.close();
}

bootstrap().catch((error) => {
  console.error('Failed to start Twenty Workspace CLI:', error);
  process.exit(1);
});
