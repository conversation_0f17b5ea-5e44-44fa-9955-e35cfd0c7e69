import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

// Import existing services from twenty-server
import { WorkspaceService } from 'twenty-server/engine/core-modules/workspace/services/workspace.service';
import { TwentyConfigService } from 'twenty-server/engine/core-modules/twenty-config/twenty-config.service';
import { LoggerService } from 'twenty-server/engine/core-modules/logger/logger.service';
import { ExceptionHandlerService } from 'twenty-server/engine/core-modules/exception-handler/exception-handler.service';
import { DomainManagerService } from 'twenty-server/engine/core-modules/domain-manager/services/domain-manager.service';

// Import entities
import { Workspace } from 'twenty-server/engine/core-modules/workspace/workspace.entity';
import { User } from 'twenty-server/engine/core-modules/user/user.entity';
import { UserWorkspace } from 'twenty-server/engine/core-modules/user-workspace/user-workspace.entity';

// Import commands
import { WorkspaceListCommand } from './commands/workspace-list.command';
import { WorkspaceHealthCommand } from './commands/workspace-health.command';
import { WorkspaceUpdateCommand } from './commands/workspace-update.command';
import { WorkspaceCreateCommand } from './commands/workspace-create.command';
import { WorkspaceDeleteCommand } from './commands/workspace-delete.command';

// Import controllers
import { WorkspaceController } from './controllers/workspace.controller';
import { HealthController } from './controllers/health.controller';

// Import utilities
import { CommandLogger } from './utils/command-logger';
import { SecurityValidator } from './utils/security-validator';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      expandVariables: true,
    }),
    TypeOrmModule.forRootAsync({
      useFactory: (configService: TwentyConfigService) => ({
        type: 'postgres',
        url: configService.get('PG_DATABASE_URL'),
        ssl: configService.get('PG_SSL_ALLOW_SELF_SIGNED')
          ? { rejectUnauthorized: false }
          : undefined,
        logging: configService.get('DEBUG_MODE'),
        schema: 'core',
        entities: [Workspace, User, UserWorkspace],
        synchronize: false,
      }),
      inject: [TwentyConfigService],
    }),
    TypeOrmModule.forFeature([Workspace, User, UserWorkspace], 'core'),
  ],
  controllers: [WorkspaceController, HealthController],
  providers: [
    // Core services from twenty-server
    TwentyConfigService,
    LoggerService,
    ExceptionHandlerService,
    WorkspaceService,
    DomainManagerService,

    // CLI-specific utilities
    CommandLogger,
    SecurityValidator,

    // Commands
    WorkspaceListCommand,
    WorkspaceHealthCommand,
    WorkspaceUpdateCommand,
    WorkspaceCreateCommand,
    WorkspaceDeleteCommand,
  ],
  exports: [WorkspaceService, CommandLogger],
})
export class WorkspaceCliModule {}
