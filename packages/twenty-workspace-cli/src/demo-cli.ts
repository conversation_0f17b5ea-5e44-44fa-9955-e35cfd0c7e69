#!/usr/bin/env node

import 'reflect-metadata';
import { CommandFactory } from 'nest-commander';
import { WorkspaceCliSimpleModule } from './workspace-cli-simple.module';

async function bootstrap() {
  const app = await CommandFactory.create(WorkspaceCliSimpleModule, {
    logger: ['error', 'warn', 'log'],
  });

  // Display CLI header
  console.log(`
╔══════════════════════════════════════════════════════╗
║                                                      ║
║    Twenty Workspace CLI - Demo Version              ║
║    Simplified workspace management tool             ║
║                                                      ║
╚══════════════════════════════════════════════════════╝
  `);

  await CommandFactory.run(app);
}

bootstrap().catch((error) => {
  console.error('Failed to start Twenty Workspace CLI:', error);
  process.exit(1);
});
