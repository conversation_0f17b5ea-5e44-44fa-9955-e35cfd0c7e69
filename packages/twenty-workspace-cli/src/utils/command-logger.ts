import { Injectable, Logger } from '@nestjs/common';
import chalk from 'chalk';

@Injectable()
export class CommandLogger {
  private readonly logger = new Logger();

  constructor(private readonly context?: string) {}

  log(message: string, context?: string): void {
    this.logger.log(chalk.blue(message), context || this.context);
  }

  error(message: string, trace?: string, context?: string): void {
    this.logger.error(chalk.red(message), trace, context || this.context);
  }

  warn(message: string, context?: string): void {
    this.logger.warn(chalk.yellow(message), context || this.context);
  }

  debug(message: string, context?: string): void {
    this.logger.debug(chalk.gray(message), context || this.context);
  }

  verbose(message: string, context?: string): void {
    this.logger.verbose(chalk.gray(message), context || this.context);
  }

  success(message: string, context?: string): void {
    this.logger.log(chalk.green(`✓ ${message}`), context || this.context);
  }

  info(message: string, context?: string): void {
    this.logger.log(chalk.cyan(`ℹ ${message}`), context || this.context);
  }

  table(data: any[]): void {
    if (data.length === 0) {
      this.warn('No data to display');
      return;
    }

    // Simple table formatting
    console.table(data);
  }

  progress(current: number, total: number, operation: string): void {
    const percentage = Math.round((current / total) * 100);
    const progressBar =
      '█'.repeat(Math.floor(percentage / 5)) +
      '░'.repeat(20 - Math.floor(percentage / 5));

    this.log(
      `${chalk.cyan(operation)}: [${chalk.green(progressBar)}] ${percentage}% (${current}/${total})`,
    );
  }
}
