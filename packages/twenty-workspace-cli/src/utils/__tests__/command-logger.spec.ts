import { CommandLogger } from '../command-logger';

// Mock console methods
const mockConsoleLog = jest.spyOn(console, 'log').mockImplementation();
const mockConsoleTable = jest.spyOn(console, 'table').mockImplementation();

describe('CommandLogger', () => {
  let logger: CommandLogger;

  beforeEach(() => {
    logger = new CommandLogger('TestContext');
    jest.clearAllMocks();
  });

  afterAll(() => {
    mockConsoleLog.mockRestore();
    mockConsoleTable.mockRestore();
  });

  it('should be defined', () => {
    expect(logger).toBeDefined();
  });

  it('should log success messages with green checkmark', () => {
    logger.success('Test success message');
    // Note: In a real test, you'd verify the logger.log was called with the right parameters
    expect(mockConsoleLog).toHaveBeenCalled();
  });

  it('should log info messages with cyan info icon', () => {
    logger.info('Test info message');
    expect(mockConsoleLog).toHaveBeenCalled();
  });

  it('should display table data', () => {
    const testData = [
      { id: 1, name: 'Test 1' },
      { id: 2, name: 'Test 2' },
    ];

    logger.table(testData);
    expect(mockConsoleTable).toHaveBeenCalledWith(testData);
  });

  it('should warn when no data to display in table', () => {
    logger.table([]);
    // Should call warn method instead of console.table
    expect(mockConsoleTable).not.toHaveBeenCalled();
  });

  it('should display progress bar correctly', () => {
    logger.progress(25, 100, 'Processing');
    expect(mockConsoleLog).toHaveBeenCalled();
  });
});
