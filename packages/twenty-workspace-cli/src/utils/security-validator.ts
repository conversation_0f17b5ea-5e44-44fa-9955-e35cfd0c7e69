import { Injectable } from '@nestjs/common';
import { TwentyConfigService } from 'twenty-server/engine/core-modules/twenty-config/twenty-config.service';

@Injectable()
export class SecurityValidator {
  constructor(private readonly configService: TwentyConfigService) {}

  /**
   * Validates that the CLI is running in an appropriate environment
   * and has necessary permissions for administrative operations
   */
  validateAdminAccess(): void {
    // Check if running in production environment
    const nodeEnv = this.configService.get('NODE_ENV');
    if (nodeEnv === 'production') {
      this.validateProductionAccess();
    }

    // Validate database access
    this.validateDatabaseAccess();
  }

  /**
   * Additional security checks for production environments
   */
  private validateProductionAccess(): void {
    // Check for required admin environment variables
    const adminKey = this.configService.get('ADMIN_CLI_KEY');
    if (!adminKey) {
      throw new Error(
        'ADMIN_CLI_KEY environment variable is required for production CLI access',
      );
    }

    // Validate admin key format (should be a strong key)
    if (adminKey.length < 32) {
      throw new Error(
        'ADMIN_CLI_KEY must be at least 32 characters long for security',
      );
    }
  }

  /**
   * Validates database connection and permissions
   */
  private validateDatabaseAccess(): void {
    const dbUrl = this.configService.get('PG_DATABASE_URL');
    if (!dbUrl) {
      throw new Error(
        'PG_DATABASE_URL is required for CLI database operations',
      );
    }

    // Additional validation could include checking database permissions
    // This is a placeholder for more sophisticated permission checks
  }

  /**
   * Validates that a workspace operation is safe to perform
   */
  validateWorkspaceOperation(
    operation: 'read' | 'update' | 'delete',
    workspaceId: string,
    options: { force?: boolean; dryRun?: boolean } = {},
  ): void {
    if (!workspaceId || workspaceId.trim().length === 0) {
      throw new Error('Workspace ID is required for all operations');
    }

    // UUID validation
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(workspaceId)) {
      throw new Error('Workspace ID must be a valid UUID');
    }

    // Additional safety checks for destructive operations
    if (operation === 'delete' && !options.force && !options.dryRun) {
      throw new Error(
        'Delete operations require either --force flag or --dry-run for safety',
      );
    }
  }

  /**
   * Validates bulk operation parameters for safety
   */
  validateBulkOperation(
    operation: string,
    batchSize = 10,
    maxBatchSize = 100,
  ): void {
    if (batchSize > maxBatchSize) {
      throw new Error(
        `Batch size ${batchSize} exceeds maximum allowed size of ${maxBatchSize}`,
      );
    }

    if (batchSize < 1) {
      throw new Error('Batch size must be at least 1');
    }

    // Log warning for large batch operations
    if (batchSize > 50) {
      console.warn(
        `Warning: Large batch size (${batchSize}) may impact system performance`,
      );
    }
  }
}
