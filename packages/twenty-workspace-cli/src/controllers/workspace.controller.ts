import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Query,
} from '@nestjs/common';
import { WorkspaceListCommand } from '../commands/workspace-list.command';
import { WorkspaceHealthCommand } from '../commands/workspace-health.command';
import { WorkspaceUpdateCommand } from '../commands/workspace-update.command';
import { WorkspaceCreateCommand } from '../commands/workspace-create.command';
import { WorkspaceDeleteCommand } from '../commands/workspace-delete.command';

interface WorkspaceCreateDto {
  subdomain: string;
  displayName?: string;
  logo?: string;
  domainName?: string;
  subscriptionStatus?: string;
  activationStatus?: string;
}

interface WorkspaceUpdateDto {
  displayName?: string;
  logo?: string;
  domainName?: string;
  subscriptionStatus?: string;
  activationStatus?: string;
}

interface WorkspaceListQuery {
  status?: string;
  domain?: string;
  limit?: number;
  offset?: number;
  format?: 'json' | 'table' | 'csv';
}

@Controller('workspaces')
export class WorkspaceController {
  constructor(
    private readonly workspaceListCommand: WorkspaceListCommand,
    private readonly workspaceHealthCommand: WorkspaceHealthCommand,
    private readonly workspaceUpdateCommand: WorkspaceUpdateCommand,
    private readonly workspaceCreateCommand: WorkspaceCreateCommand,
    private readonly workspaceDeleteCommand: WorkspaceDeleteCommand,
  ) {}

  @Get()
  async listWorkspaces(@Query() query: WorkspaceListQuery) {
    // Convert query parameters to command options
    const options = {
      status: query.status,
      domain: query.domain,
      limit: query.limit ? String(query.limit) : undefined,
      offset: query.offset ? String(query.offset) : undefined,
      format: query.format || 'json',
    };

    try {
      const result = await this.workspaceListCommand.run([], options);
      return {
        success: true,
        data: result,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  @Post()
  async createWorkspace(@Body() createDto: WorkspaceCreateDto) {
    try {
      const result = await this.workspaceCreateCommand.run(
        [createDto.subdomain],
        {
          displayName: createDto.displayName,
          logo: createDto.logo,
          domainName: createDto.domainName,
          subscriptionStatus: createDto.subscriptionStatus,
          activationStatus: createDto.activationStatus,
        },
      );

      return {
        success: true,
        data: result,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  @Put(':id')
  async updateWorkspace(
    @Param('id') id: string,
    @Body() updateDto: WorkspaceUpdateDto,
  ) {
    try {
      const result = await this.workspaceUpdateCommand.run([id], updateDto);
      return {
        success: true,
        data: result,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  @Delete(':id')
  async deleteWorkspace(
    @Param('id') id: string,
    @Query('force') force?: string,
    @Query('soft') soft?: string,
    @Query('hard') hard?: string,
  ) {
    try {
      const result = await this.workspaceDeleteCommand.run([], {
        workspaceId: id,
        force: force === 'true',
        soft: soft !== 'false', // default to true unless explicitly false
        hard: hard === 'true',
      });

      return {
        success: true,
        data: result,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  @Get(':id/health')
  async checkWorkspaceHealth(
    @Param('id') id: string,
    @Query('category') category?: string,
    @Query('fix') fix?: string,
    @Query('dryRun') dryRun?: string,
  ) {
    try {
      const result = await this.workspaceHealthCommand.run([id], {
        category,
        fix: fix === 'true',
        dryRun: dryRun === 'true',
      });

      return {
        success: true,
        data: result,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }
}
