import { NestFactory } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import { Module, Controller, Get, Injectable } from '@nestjs/common';

@Injectable()
class SimpleWorkspaceService {
  getWorkspaces() {
    return [
      {
        id: '1',
        subdomain: 'demo',
        displayName: 'Demo Workspace',
        status: 'active',
        createdAt: new Date(),
      },
      {
        id: '2',
        subdomain: 'test',
        displayName: 'Test Workspace',
        status: 'active',
        createdAt: new Date(),
      },
    ];
  }
}

@Controller('health')
class HealthController {
  @Get()
  getHealth() {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'twenty-workspace-cli',
      version: '1.0.0',
    };
  }
}

@Controller('workspaces')
class WorkspaceController {
  constructor(private readonly workspaceService: SimpleWorkspaceService) {}

  @Get()
  getWorkspaces() {
    return {
      success: true,
      data: this.workspaceService.getWorkspaces(),
      timestamp: new Date().toISOString(),
    };
  }
}

@Module({
  controllers: [HealthController, WorkspaceController],
  providers: [SimpleWorkspaceService],
})
class SimpleWorkspaceModule {}

const bootstrap = async () => {
  const app = await NestFactory.create<NestExpressApplication>(
    SimpleWorkspaceModule,
    {
      cors: true,
      bufferLogs: false,
    },
  );

  app.useBodyParser('json', { limit: '10mb' });
  app.useBodyParser('urlencoded', {
    limit: '10mb',
    extended: true,
  });

  const port = process.env.PORT || 3002;

  console.log(`🚀 Twenty Workspace CLI API starting on port ${port}`);
  console.log(`📋 Available endpoints:`);
  console.log(`   GET    /health - Health check`);
  console.log(`   GET    /workspaces - List workspaces`);

  await app.listen(port);
};

bootstrap().catch((error) => {
  console.error('❌ Failed to start Twenty Workspace CLI API:', error);
  process.exit(1);
});
