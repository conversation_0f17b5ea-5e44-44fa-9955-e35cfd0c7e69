import { Command, CommandRunner, Op<PERSON> } from 'nest-commander';
import { Injectable, Logger } from '@nestjs/common';
import { WorkspaceService } from 'twenty-server/engine/core-modules/workspace/services/workspace.service';
import { CommandLogger } from '../utils/command-logger';
import { workspaceValidator } from 'twenty-server/engine/core-modules/workspace/workspace.validate';
import chalk from 'chalk';

interface WorkspaceDeleteCommandOptions {
  workspaceId: string;
  soft?: boolean;
  hard?: boolean;
  force?: boolean;
  confirm?: boolean;
}

@Injectable()
@Command({
  name: 'workspace:delete',
  description: 'Delete a workspace with safety confirmations',
  aliases: ['ws:delete', 'delete'],
})
export class WorkspaceDeleteCommand extends CommandRunner {
  private readonly logger = new Logger(WorkspaceDeleteCommand.name);

  constructor(
    private readonly workspaceService: WorkspaceService,
    private readonly commandLogger: CommandLogger,
  ) {
    super();
  }

  async run(
    _passedParam: string[],
    options: WorkspaceDeleteCommandOptions,
  ): Promise<void> {
    // Handle --hard flag to override --soft default
    if (options.hard) {
      options.soft = false;
    }
    try {
      this.commandLogger.info(
        `Preparing to delete workspace: ${options.workspaceId}`,
      );

      // Validate workspace exists
      const workspace = await this.workspaceService.findOneBy({
        id: options.workspaceId,
      });
      workspaceValidator.assertIsDefinedOrThrow(workspace);

      this.commandLogger.success(
        `Found workspace: ${workspace.displayName} (${workspace.subdomain})`,
      );

      // Show workspace details and impact
      await this.showDeletionImpact(workspace, options.soft);

      // Safety confirmation
      if (!options.force) {
        const confirmed = await this.confirmDeletion(workspace, options.soft);
        if (!confirmed) {
          this.commandLogger.info('Deletion cancelled');
          return;
        }
      } else {
        this.commandLogger.warn('Force deletion mode - skipping confirmations');
      }

      // Perform deletion
      const deletionType = options.soft ? 'soft' : 'hard';
      this.commandLogger.info(`Performing ${deletionType} deletion...`);

      const deletedWorkspace = await this.workspaceService.deleteWorkspace(
        options.workspaceId,
        options.soft,
      );

      this.commandLogger.success(
        `Workspace ${deletionType} deleted successfully`,
      );
      this.showDeletionSummary(deletedWorkspace, options.soft);
    } catch (error) {
      this.commandLogger.error('Failed to delete workspace', error.message);
      throw error;
    }
  }

  private async showDeletionImpact(
    workspace: any,
    isSoftDelete: boolean,
  ): Promise<void> {
    this.commandLogger.warn('DELETION IMPACT ANALYSIS:');
    console.log();

    // Workspace details
    console.log(chalk.bold('Workspace Details:'));
    console.log(`  ID: ${workspace.id}`);
    console.log(`  Name: ${workspace.displayName}`);
    console.log(`  Subdomain: ${workspace.subdomain}`);
    console.log(`  Status: ${workspace.activationStatus}`);
    console.log(`  Created: ${workspace.createdAt}`);
    if (workspace.customDomain) {
      console.log(`  Custom Domain: ${workspace.customDomain}`);
    }
    console.log();

    // Impact assessment
    console.log(
      chalk.bold(
        isSoftDelete ? 'Soft Deletion Impact:' : 'HARD DELETION IMPACT:',
      ),
    );

    if (isSoftDelete) {
      console.log(
        chalk.yellow(
          '  ⚠ Workspace will be marked as deleted but data preserved',
        ),
      );
      console.log(chalk.yellow('  ⚠ Users will lose access to the workspace'));
      console.log(
        chalk.yellow('  ⚠ Subdomain will become available for reuse'),
      );
      console.log(chalk.green('  ✓ Data can be recovered if needed'));
      console.log(chalk.green('  ✓ Custom domain will be preserved'));
    } else {
      console.log(
        chalk.red('  ✗ ALL WORKSPACE DATA WILL BE PERMANENTLY DELETED'),
      );
      console.log(
        chalk.red(
          '  ✗ User accounts will be removed if this is their only workspace',
        ),
      );
      console.log(chalk.red('  ✗ Files and attachments will be deleted'));
      console.log(chalk.red('  ✗ Custom domain configuration will be removed'));
      console.log(chalk.red('  ✗ THIS ACTION CANNOT BE UNDONE'));
    }
    console.log();

    // Additional warnings for hard deletion
    if (!isSoftDelete) {
      console.log(
        chalk.red(chalk.bold('⚠ WARNING: HARD DELETION IS IRREVERSIBLE ⚠')),
      );
      console.log(
        chalk.red('Consider using --soft flag for soft deletion instead'),
      );
      console.log();
    }
  }

  private async confirmDeletion(
    workspace: any,
    isSoftDelete: boolean,
  ): Promise<boolean> {
    const deletionType = isSoftDelete ? 'soft delete' : 'PERMANENTLY DELETE';

    console.log(
      chalk.yellow(
        `To confirm ${deletionType}, please type the workspace subdomain: ${chalk.bold(workspace.subdomain)}`,
      ),
    );

    // In a real implementation, you'd use inquirer or similar for interactive input
    // For now, we'll check for a confirmation flag
    // This is a simplified version - in practice you'd want proper interactive confirmation

    this.commandLogger.warn(
      `Please confirm you want to ${deletionType} workspace "${workspace.displayName}" (${workspace.subdomain})`,
    );

    // TODO: Implement proper interactive confirmation using inquirer or similar
    // For now, we require explicit confirmation via --force flag
    console.log(
      chalk.yellow('Use --force flag to skip this confirmation step'),
    );
    return false;
  }

  private showDeletionSummary(workspace: any, isSoftDelete: boolean): void {
    console.log();
    this.commandLogger.info('Deletion Summary:');

    const status = isSoftDelete ? 'Soft Deleted' : 'Hard Deleted';
    const details = {
      'Workspace ID': workspace.id,
      'Display Name': workspace.displayName,
      Subdomain: workspace.subdomain,
      Status: status,
      'Deleted At': workspace.deletedAt || 'Just now',
    };

    console.table(details);

    if (isSoftDelete) {
      console.log(
        chalk.green('Workspace has been soft deleted. Data is preserved.'),
      );
      console.log(
        chalk.cyan(
          'To permanently delete, use: workspace:delete --workspace-id ' +
            workspace.id +
            ' --hard',
        ),
      );
    } else {
      console.log(
        chalk.red(
          'Workspace has been permanently deleted. This action cannot be undone.',
        ),
      );
    }

    console.log();
    this.commandLogger.info('Deletion completed successfully');
  }

  @Option({
    flags: '-w, --workspace-id <workspaceId>',
    description: 'Workspace ID to delete',
    required: true,
  })
  parseWorkspaceId(value: string): string {
    if (!value || value.trim().length === 0) {
      throw new Error('Workspace ID is required');
    }
    return value.trim();
  }

  @Option({
    flags: '--soft',
    description: 'Perform soft deletion (preserves data)',
    defaultValue: true,
  })
  parseSoft(): boolean {
    return true;
  }

  @Option({
    flags: '--force',
    description: 'Skip safety confirmations (USE WITH EXTREME CAUTION)',
  })
  parseForce(): boolean {
    return true;
  }

  @Option({
    flags: '--hard',
    description: 'Perform hard deletion (PERMANENTLY DELETES ALL DATA)',
  })
  parseHard(): boolean {
    // This indicates hard deletion was requested
    return true;
  }
}
