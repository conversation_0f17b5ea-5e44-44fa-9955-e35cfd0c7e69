import { Command, CommandRunner, Option } from 'nest-commander';
import { Injectable, Logger } from '@nestjs/common';
import { WorkspaceService } from 'twenty-server/engine/core-modules/workspace/services/workspace.service';
import { CommandLogger } from '../utils/command-logger';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Workspace } from 'twenty-server/engine/core-modules/workspace/workspace.entity';
import { User } from 'twenty-server/engine/core-modules/user/user.entity';
import { WorkspaceActivationStatus } from 'twenty-shared/workspace';
import chalk from 'chalk';

interface WorkspaceCreateCommandOptions {
  displayName: string;
  subdomain: string;
  customDomain?: string;
  ownerEmail?: string;
  activate?: boolean;
}

@Injectable()
@Command({
  name: 'workspace:create',
  description: 'Create a new workspace with proper initialization',
  aliases: ['ws:create', 'create'],
})
export class WorkspaceCreateCommand extends CommandRunner {
  private readonly logger = new Logger(WorkspaceCreateCommand.name);

  constructor(
    private readonly workspaceService: WorkspaceService,
    private readonly commandLogger: CommandLogger,
    @InjectRepository(Workspace, 'core')
    private readonly workspaceRepository: Repository<Workspace>,
    @InjectRepository(User, 'core')
    private readonly userRepository: Repository<User>,
  ) {
    super();
  }

  async run(
    _passedParam: string[],
    options: WorkspaceCreateCommandOptions,
  ): Promise<void> {
    try {
      this.commandLogger.info('Creating new workspace...');

      // Validate subdomain availability
      await this.validateSubdomainAvailability(options.subdomain);

      // Find owner user if email provided
      let ownerUser: User | null = null;
      if (options.ownerEmail) {
        ownerUser = await this.findUserByEmail(options.ownerEmail);
        if (!ownerUser) {
          throw new Error(`User with email ${options.ownerEmail} not found`);
        }
        this.commandLogger.success(`Found owner user: ${ownerUser.email}`);
      }

      // Create workspace entity
      const workspace = await this.createWorkspaceEntity(options);
      this.commandLogger.success(`Workspace created with ID: ${workspace.id}`);

      // Activate workspace if requested
      if (options.activate && ownerUser) {
        await this.activateWorkspace(workspace, ownerUser, options);
      }

      // Display created workspace
      this.displayCreatedWorkspace(workspace);
    } catch (error) {
      this.commandLogger.error('Failed to create workspace', error.message);
      throw error;
    }
  }

  private async validateSubdomainAvailability(
    subdomain: string,
  ): Promise<void> {
    this.commandLogger.info(`Checking subdomain availability: ${subdomain}`);

    const isAvailable =
      await this.workspaceService.isSubdomainAvailable(subdomain);

    if (!isAvailable) {
      throw new Error(`Subdomain "${subdomain}" is already taken`);
    }

    this.commandLogger.success('Subdomain is available');
  }

  private async findUserByEmail(email: string): Promise<User | null> {
    this.commandLogger.info(`Looking up user: ${email}`);

    return await this.userRepository.findOne({
      where: { email: email.toLowerCase() },
    });
  }

  private async createWorkspaceEntity(
    options: WorkspaceCreateCommandOptions,
  ): Promise<Workspace> {
    const workspace = this.workspaceRepository.create({
      displayName: options.displayName,
      subdomain: options.subdomain,
      customDomain: options.customDomain || null,
      activationStatus: options.activate
        ? WorkspaceActivationStatus.PENDING_CREATION
        : WorkspaceActivationStatus.INACTIVE,
      // Default settings
      allowImpersonation: true,
      isPublicInviteLinkEnabled: true,
      isGoogleAuthEnabled: true,
      isPasswordAuthEnabled: true,
      isMicrosoftAuthEnabled: true,
      isCustomDomainEnabled: false,
      metadataVersion: 1,
    });

    return await this.workspaceRepository.save(workspace);
  }

  private async activateWorkspace(
    workspace: Workspace,
    ownerUser: User,
    options: WorkspaceCreateCommandOptions,
  ): Promise<void> {
    this.commandLogger.info('Activating workspace...');

    try {
      // Use existing activation service
      const activatedWorkspace = await this.workspaceService.activateWorkspace(
        ownerUser,
        workspace,
        {
          displayName: options.displayName,
        },
      );

      this.commandLogger.success('Workspace activated successfully');

      // Update local workspace reference
      Object.assign(workspace, activatedWorkspace);
    } catch (error) {
      this.commandLogger.error('Failed to activate workspace', error.message);
      this.commandLogger.warn(
        'Workspace created but not activated. You can activate it later.',
      );
    }
  }

  private displayCreatedWorkspace(workspace: Workspace): void {
    this.commandLogger.info('Workspace creation summary:');
    console.log();

    const details = {
      ID: workspace.id,
      'Display Name': workspace.displayName,
      Subdomain: workspace.subdomain,
      'Custom Domain': workspace.customDomain || 'None',
      Status: workspace.activationStatus,
      'Created At': workspace.createdAt,
    };

    console.table(details);

    // Show URLs
    console.log(chalk.bold('Access URLs:'));
    console.log(
      `  Subdomain URL: ${chalk.blue(`https://${workspace.subdomain}.example.com`)}`,
    );
    if (workspace.customDomain) {
      console.log(
        `  Custom Domain: ${chalk.blue(`https://${workspace.customDomain}`)}`,
      );
    }
    console.log();

    // Show next steps
    if (workspace.activationStatus === WorkspaceActivationStatus.INACTIVE) {
      console.log(chalk.yellow('Next steps:'));
      console.log(
        '  1. Activate the workspace using: workspace:activate --workspace-id ' +
          workspace.id,
      );
      console.log('  2. Add users to the workspace');
      console.log('  3. Configure workspace settings');
    } else if (
      workspace.activationStatus === WorkspaceActivationStatus.ACTIVE
    ) {
      console.log(chalk.green('Workspace is ready to use!'));
    }
  }

  @Option({
    flags: '-n, --display-name <displayName>',
    description: 'Workspace display name',
    required: true,
  })
  parseDisplayName(value: string): string {
    if (!value || value.trim().length === 0) {
      throw new Error('Display name is required');
    }
    return value.trim();
  }

  @Option({
    flags: '-s, --subdomain <subdomain>',
    description: 'Workspace subdomain (must be unique)',
    required: true,
  })
  parseSubdomain(value: string): string {
    if (!value || value.trim().length === 0) {
      throw new Error('Subdomain is required');
    }

    const subdomain = value.trim().toLowerCase();

    // Basic validation
    if (!/^[a-z0-9][a-z0-9-]{1,28}[a-z0-9]$/.test(subdomain)) {
      throw new Error(
        'Subdomain must be 3-30 characters, lowercase letters, numbers, and hyphens only',
      );
    }

    if (subdomain.startsWith('api-')) {
      throw new Error('Subdomain cannot start with "api-"');
    }

    return subdomain;
  }

  @Option({
    flags: '-d, --custom-domain <customDomain>',
    description: 'Custom domain for the workspace',
  })
  parseCustomDomain(value: string): string {
    // Basic domain validation
    const domainRegex =
      /^(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9])\.)*([A-Za-z0-9]|[A-Za-z0-9][A-Za-z0-9-]*[A-Za-z0-9])$/;

    if (!domainRegex.test(value)) {
      throw new Error('Invalid domain format');
    }

    return value.toLowerCase();
  }

  @Option({
    flags: '-o, --owner-email <ownerEmail>',
    description: 'Email of the workspace owner (must be existing user)',
  })
  parseOwnerEmail(value: string): string {
    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (!emailRegex.test(value)) {
      throw new Error('Invalid email format');
    }

    return value.toLowerCase();
  }

  @Option({
    flags: '--activate',
    description:
      'Activate the workspace immediately after creation (requires owner email)',
  })
  parseActivate(): boolean {
    return true;
  }
}
