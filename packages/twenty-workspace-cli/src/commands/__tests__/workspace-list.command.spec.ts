import { Test, TestingModule } from '@nestjs/testing';
import { WorkspaceListCommand } from '../workspace-list.command';
import { WorkspaceService } from 'twenty-server/engine/core-modules/workspace/services/workspace.service';
import { CommandLogger } from '../../utils/command-logger';

describe('WorkspaceListCommand', () => {
  let command: WorkspaceListCommand;
  let workspaceService: jest.Mocked<WorkspaceService>;
  let commandLogger: jest.Mocked<CommandLogger>;

  beforeEach(async () => {
    const mockWorkspaceService = {
      find: jest.fn(),
    };

    const mockCommandLogger = {
      info: jest.fn(),
      success: jest.fn(),
      table: jest.fn(),
      log: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorkspaceListCommand,
        {
          provide: WorkspaceService,
          useValue: mockWorkspaceService,
        },
        {
          provide: CommandLogger,
          useValue: mockCommandLogger,
        },
      ],
    }).compile();

    command = module.get<WorkspaceListCommand>(WorkspaceListCommand);
    workspaceService = module.get(WorkspaceService);
    commandLogger = module.get(CommandLogger);
  });

  it('should be defined', () => {
    expect(command).toBeDefined();
  });

  it('should list workspaces with default options', async () => {
    const mockWorkspaces = [
      {
        id: '1',
        displayName: 'Test Workspace 1',
        subdomain: 'test1',
        activationStatus: 'ACTIVE',
      },
      {
        id: '2',
        displayName: 'Test Workspace 2',
        subdomain: 'test2',
        activationStatus: 'ACTIVE',
      },
    ];

    workspaceService.find.mockResolvedValue(mockWorkspaces);

    await command.run([], {});

    expect(workspaceService.find).toHaveBeenCalled();
    expect(commandLogger.success).toHaveBeenCalledWith('Found 2 workspaces');
  });

  it('should filter workspaces by status', async () => {
    const mockWorkspaces = [
      {
        id: '1',
        displayName: 'Active Workspace',
        subdomain: 'active',
        activationStatus: 'ACTIVE',
      },
    ];

    workspaceService.find.mockResolvedValue(mockWorkspaces);

    await command.run([], { status: 'active' });

    expect(workspaceService.find).toHaveBeenCalledWith({
      where: { activationStatus: 'ACTIVE' },
      take: 50,
      skip: 0,
    });
  });
});
