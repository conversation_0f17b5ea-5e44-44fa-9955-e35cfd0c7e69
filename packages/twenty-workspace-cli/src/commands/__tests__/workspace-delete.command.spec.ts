import { Test, TestingModule } from '@nestjs/testing';
import { WorkspaceDeleteCommand } from '../workspace-delete.command';
import { WorkspaceService } from 'twenty-server/engine/core-modules/workspace/services/workspace.service';
import { CommandLogger } from '../../utils/command-logger';

describe('WorkspaceDeleteCommand', () => {
  let command: WorkspaceDeleteCommand;
  let workspaceService: jest.Mocked<WorkspaceService>;
  let commandLogger: jest.Mocked<CommandLogger>;

  beforeEach(async () => {
    const mockWorkspaceService = {
      findOneBy: jest.fn(),
      deleteWorkspace: jest.fn(),
    };

    const mockCommandLogger = {
      info: jest.fn(),
      success: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorkspaceDeleteCommand,
        {
          provide: WorkspaceService,
          useValue: mockWorkspaceService,
        },
        {
          provide: CommandLogger,
          useValue: mockCommandLogger,
        },
      ],
    }).compile();

    command = module.get<WorkspaceDeleteCommand>(WorkspaceDeleteCommand);
    workspaceService = module.get(WorkspaceService);
    commandLogger = module.get(CommandLogger);
  });

  it('should be defined', () => {
    expect(command).toBeDefined();
  });

  it('should throw error when workspace not found', async () => {
    workspaceService.findOneBy.mockResolvedValue(null);

    await expect(
      command.run([], {
        workspaceId: 'non-existent-id',
        force: true,
      }),
    ).rejects.toThrow();
  });

  it('should perform soft delete by default', async () => {
    const mockWorkspace = {
      id: 'test-id',
      displayName: 'Test Workspace',
      subdomain: 'test',
    };

    workspaceService.findOneBy.mockResolvedValue(mockWorkspace);
    workspaceService.deleteWorkspace.mockResolvedValue(mockWorkspace);

    await command.run([], {
      workspaceId: 'test-id',
      force: true,
      soft: true,
    });

    expect(workspaceService.deleteWorkspace).toHaveBeenCalledWith(
      'test-id',
      true,
    );
  });

  it('should perform hard delete when hard flag is set', async () => {
    const mockWorkspace = {
      id: 'test-id',
      displayName: 'Test Workspace',
      subdomain: 'test',
    };

    workspaceService.findOneBy.mockResolvedValue(mockWorkspace);
    workspaceService.deleteWorkspace.mockResolvedValue(mockWorkspace);

    await command.run([], {
      workspaceId: 'test-id',
      force: true,
      hard: true,
    });

    expect(workspaceService.deleteWorkspace).toHaveBeenCalledWith(
      'test-id',
      false,
    );
  });
});
