import { Command, CommandRunner, Option } from 'nest-commander';
import { Injectable, Logger } from '@nestjs/common';
import { WorkspaceService } from 'twenty-server/engine/core-modules/workspace/services/workspace.service';
import { WorkspaceActivationStatus } from 'twenty-shared/workspace';
import { CommandLogger } from '../utils/command-logger';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Workspace } from 'twenty-server/engine/core-modules/workspace/workspace.entity';

interface WorkspaceListCommandOptions {
  status?: string;
  limit?: number;
  offset?: number;
  subdomain?: string;
  customDomain?: string;
  format?: string;
  verbose?: boolean;
}

@Injectable()
@Command({
  name: 'workspace:list',
  description: 'List workspaces with filtering and formatting options',
  aliases: ['ws:list', 'list'],
})
export class WorkspaceListCommand extends CommandRunner {
  private readonly logger = new Logger(WorkspaceListCommand.name);

  constructor(
    private readonly workspaceService: WorkspaceService,
    private readonly commandLogger: CommandLogger,
    @InjectRepository(Workspace, 'core')
    private readonly workspaceRepository: Repository<Workspace>,
  ) {
    super();
  }

  async run(
    _passedParam: string[],
    options: WorkspaceListCommandOptions,
  ): Promise<void> {
    try {
      this.commandLogger.info('Fetching workspaces...');

      // Build query based on options
      const queryBuilder =
        this.workspaceRepository.createQueryBuilder('workspace');

      // Apply status filter
      if (options.status && options.status !== 'all') {
        const statusMap = {
          active: WorkspaceActivationStatus.ACTIVE,
          inactive: WorkspaceActivationStatus.INACTIVE,
          pending: WorkspaceActivationStatus.PENDING_CREATION,
          ongoing: WorkspaceActivationStatus.ONGOING_CREATION,
        };

        const status = statusMap[options.status as keyof typeof statusMap];
        if (status) {
          queryBuilder.where('workspace.activationStatus = :status', {
            status,
          });
        }
      }

      // Apply subdomain filter
      if (options.subdomain) {
        queryBuilder.andWhere('workspace.subdomain ILIKE :subdomain', {
          subdomain: `%${options.subdomain}%`,
        });
      }

      // Apply custom domain filter
      if (options.customDomain === 'true') {
        queryBuilder.andWhere('workspace.customDomain IS NOT NULL');
      } else if (options.customDomain === 'false') {
        queryBuilder.andWhere('workspace.customDomain IS NULL');
      }

      // Apply pagination
      if (options.limit) {
        queryBuilder.limit(options.limit);
      }
      if (options.offset) {
        queryBuilder.offset(options.offset);
      }

      // Execute query
      const workspaces = await queryBuilder.getMany();

      if (workspaces.length === 0) {
        this.commandLogger.warn('No workspaces found matching criteria');
        return;
      }

      // Format output
      await this.formatOutput(workspaces, options);

      this.commandLogger.success(`Found ${workspaces.length} workspace(s)`);
    } catch (error) {
      this.commandLogger.error('Failed to list workspaces', error.message);
      throw error;
    }
  }

  private async formatOutput(
    workspaces: Workspace[],
    options: WorkspaceListCommandOptions,
  ): Promise<void> {
    const format = options.format || 'table';

    switch (format) {
      case 'json':
        console.log(JSON.stringify(workspaces, null, 2));
        break;

      case 'csv':
        this.outputCsv(workspaces, options.verbose);
        break;

      case 'table':
      default:
        this.outputTable(workspaces, options.verbose);
        break;
    }
  }

  private outputTable(workspaces: Workspace[], verbose = false): void {
    const tableData = workspaces.map((workspace) => {
      const baseData = {
        ID: workspace.id.substring(0, 8) + '...',
        'Display Name': workspace.displayName || 'N/A',
        Subdomain: workspace.subdomain,
        Status: workspace.activationStatus,
        Created: workspace.createdAt.toISOString().split('T')[0],
      };

      if (verbose) {
        return {
          ...baseData,
          'Custom Domain': workspace.customDomain || 'N/A',
          'Google Auth': workspace.isGoogleAuthEnabled ? '✓' : '✗',
          'Password Auth': workspace.isPasswordAuthEnabled ? '✓' : '✗',
          'Microsoft Auth': workspace.isMicrosoftAuthEnabled ? '✓' : '✗',
          'Public Invite': workspace.isPublicInviteLinkEnabled ? '✓' : '✗',
          Version: workspace.version || 'N/A',
        };
      }

      return baseData;
    });

    this.commandLogger.table(tableData);
  }

  private outputCsv(workspaces: Workspace[], verbose = false): void {
    const headers = verbose
      ? [
          'ID',
          'Display Name',
          'Subdomain',
          'Status',
          'Created',
          'Custom Domain',
          'Google Auth',
          'Password Auth',
          'Microsoft Auth',
          'Public Invite',
          'Version',
        ]
      : ['ID', 'Display Name', 'Subdomain', 'Status', 'Created'];

    console.log(headers.join(','));

    workspaces.forEach((workspace) => {
      const baseData = [
        workspace.id,
        `"${workspace.displayName || ''}"`,
        workspace.subdomain,
        workspace.activationStatus,
        workspace.createdAt.toISOString().split('T')[0],
      ];

      if (verbose) {
        baseData.push(
          `"${workspace.customDomain || ''}"`,
          workspace.isGoogleAuthEnabled ? 'true' : 'false',
          workspace.isPasswordAuthEnabled ? 'true' : 'false',
          workspace.isMicrosoftAuthEnabled ? 'true' : 'false',
          workspace.isPublicInviteLinkEnabled ? 'true' : 'false',
          `"${workspace.version || ''}"`,
        );
      }

      console.log(baseData.join(','));
    });
  }

  @Option({
    flags: '-s, --status <status>',
    description:
      'Filter by activation status (active, inactive, pending, ongoing, all)',
    defaultValue: 'all',
  })
  parseStatus(value: string): string {
    const validStatuses = ['active', 'inactive', 'pending', 'ongoing', 'all'];
    if (!validStatuses.includes(value)) {
      throw new Error(
        `Invalid status. Must be one of: ${validStatuses.join(', ')}`,
      );
    }
    return value;
  }

  @Option({
    flags: '-l, --limit <number>',
    description: 'Limit number of results',
  })
  parseLimit(value: string): number {
    const limit = parseInt(value, 10);
    if (isNaN(limit) || limit <= 0) {
      throw new Error('Limit must be a positive number');
    }
    return limit;
  }

  @Option({
    flags: '-o, --offset <number>',
    description: 'Offset for pagination',
  })
  parseOffset(value: string): number {
    const offset = parseInt(value, 10);
    if (isNaN(offset) || offset < 0) {
      throw new Error('Offset must be a non-negative number');
    }
    return offset;
  }

  @Option({
    flags: '--subdomain <subdomain>',
    description: 'Filter by subdomain (partial match)',
  })
  parseSubdomain(value: string): string {
    return value;
  }

  @Option({
    flags: '--custom-domain <boolean>',
    description: 'Filter by custom domain presence (true/false)',
  })
  parseCustomDomain(value: string): string {
    if (!['true', 'false'].includes(value)) {
      throw new Error('Custom domain filter must be "true" or "false"');
    }
    return value;
  }

  @Option({
    flags: '-f, --format <format>',
    description: 'Output format (table, json, csv)',
    defaultValue: 'table',
  })
  parseFormat(value: string): string {
    const validFormats = ['table', 'json', 'csv'];
    if (!validFormats.includes(value)) {
      throw new Error(
        `Invalid format. Must be one of: ${validFormats.join(', ')}`,
      );
    }
    return value;
  }

  @Option({
    flags: '-v, --verbose',
    description: 'Show additional workspace details',
  })
  parseVerbose(): boolean {
    return true;
  }
}
