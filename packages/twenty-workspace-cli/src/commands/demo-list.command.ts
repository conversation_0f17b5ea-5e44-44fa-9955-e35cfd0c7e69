import { Command, CommandRunner, Op<PERSON> } from 'nest-commander';
import { Injectable, Logger } from '@nestjs/common';
import { CommandLogger } from '../utils/command-logger';
import {
  SimpleWorkspaceService,
  SimpleWorkspace,
} from '../workspace-cli-simple.module';

interface DemoListCommandOptions {
  format?: string;
  limit?: number;
}

@Injectable()
@Command({
  name: 'demo:list',
  description: 'Demo command to list workspaces (simplified version)',
  aliases: ['demo', 'test'],
})
export class DemoListCommand extends CommandRunner {
  private readonly logger = new Logger(DemoListCommand.name);

  constructor(
    private readonly workspaceService: SimpleWorkspaceService,
    private readonly commandLogger: CommandLogger,
  ) {
    super();
  }

  async run(
    _passedParam: string[],
    options: DemoListCommandOptions,
  ): Promise<void> {
    try {
      this.commandLogger.info('Fetching workspaces (demo mode)...');

      // Fetch workspaces
      let workspaces = await this.workspaceService.findAll();

      // Apply limit if specified
      if (options.limit) {
        workspaces = workspaces.slice(0, options.limit);
      }

      if (workspaces.length === 0) {
        this.commandLogger.warn('No workspaces found');
        return;
      }

      // Format output
      this.formatOutput(workspaces, options.format);

      this.commandLogger.success(`Found ${workspaces.length} workspace(s)`);
    } catch (error) {
      this.commandLogger.error('Failed to list workspaces', error.message);
      throw error;
    }
  }

  private formatOutput(workspaces: SimpleWorkspace[], format = 'table'): void {
    switch (format) {
      case 'json':
        console.log(JSON.stringify(workspaces, null, 2));
        break;

      case 'table':
      default:
        const tableData = workspaces.map((workspace) => ({
          ID: workspace.id.substring(0, 8) + '...',
          'Display Name': workspace.displayName || 'N/A',
          Subdomain: workspace.subdomain,
          Status: workspace.activationStatus,
          Created: workspace.createdAt.toISOString().split('T')[0],
        }));

        this.commandLogger.table(tableData);
        break;
    }
  }

  @Option({
    flags: '-f, --format <format>',
    description: 'Output format (table, json)',
    defaultValue: 'table',
  })
  parseFormat(value: string): string {
    const validFormats = ['table', 'json'];
    if (!validFormats.includes(value)) {
      throw new Error(
        `Invalid format. Must be one of: ${validFormats.join(', ')}`,
      );
    }
    return value;
  }

  @Option({
    flags: '-l, --limit <number>',
    description: 'Limit number of results',
  })
  parseLimit(value: string): number {
    const limit = parseInt(value, 10);
    if (isNaN(limit) || limit <= 0) {
      throw new Error('Limit must be a positive number');
    }
    return limit;
  }
}
