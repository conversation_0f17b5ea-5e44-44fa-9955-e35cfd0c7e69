import { Command, CommandRunner, Op<PERSON> } from 'nest-commander';
import { Injectable, Logger } from '@nestjs/common';
import { WorkspaceService } from 'twenty-server/engine/core-modules/workspace/services/workspace.service';
import { DomainManagerService } from 'twenty-server/engine/core-modules/domain-manager/services/domain-manager.service';
import { CommandLogger } from '../utils/command-logger';
import { workspaceValidator } from 'twenty-server/engine/core-modules/workspace/workspace.validate';
import chalk from 'chalk';

interface WorkspaceHealthCommandOptions {
  workspaceId: string;
  fix?: boolean;
  dryRun?: boolean;
  checkDomain?: boolean;
  checkMetadata?: boolean;
  checkUsers?: boolean;
}

interface HealthCheckResult {
  category: string;
  status: 'healthy' | 'warning' | 'error';
  message: string;
  fixable?: boolean;
  details?: any;
}

@Injectable()
@Command({
  name: 'workspace:health',
  description: 'Check and validate workspace health with optional fixing',
  aliases: ['ws:health', 'health'],
})
export class WorkspaceHealthCommand extends CommandRunner {
  private readonly logger = new Logger(WorkspaceHealthCommand.name);

  constructor(
    private readonly workspaceService: WorkspaceService,
    private readonly domainManagerService: DomainManagerService,
    private readonly commandLogger: CommandLogger,
  ) {
    super();
  }

  async run(
    _passedParam: string[],
    options: WorkspaceHealthCommandOptions,
  ): Promise<void> {
    try {
      this.commandLogger.info(
        `Starting health check for workspace: ${options.workspaceId}`,
      );

      // Validate workspace exists
      const workspace = await this.workspaceService.findById(
        options.workspaceId,
      );
      workspaceValidator.assertIsDefinedOrThrow(workspace);

      this.commandLogger.success(
        `Found workspace: ${workspace.displayName} (${workspace.subdomain})`,
      );

      // Perform health checks
      const results: HealthCheckResult[] = [];

      // Basic workspace validation
      results.push(...(await this.checkBasicWorkspaceHealth(workspace)));

      // Domain checks (if enabled)
      if (options.checkDomain !== false) {
        results.push(...(await this.checkDomainHealth(workspace)));
      }

      // Metadata checks (if enabled)
      if (options.checkMetadata !== false) {
        results.push(...(await this.checkMetadataHealth(workspace)));
      }

      // User/membership checks (if enabled)
      if (options.checkUsers !== false) {
        results.push(...(await this.checkUserHealth(workspace)));
      }

      // Display results
      this.displayHealthResults(results);

      // Apply fixes if requested
      if (options.fix && !options.dryRun) {
        await this.applyFixes(workspace, results);
      } else if (options.fix && options.dryRun) {
        this.commandLogger.info(
          'Dry run mode: Would apply the following fixes:',
        );
        this.showFixableIssues(results);
      }

      // Summary
      const summary = this.generateSummary(results);
      this.displaySummary(summary);
    } catch (error) {
      this.commandLogger.error('Health check failed', error.message);
      throw error;
    }
  }

  private async checkBasicWorkspaceHealth(
    workspace: any,
  ): Promise<HealthCheckResult[]> {
    const results: HealthCheckResult[] = [];

    // Check activation status
    results.push({
      category: 'Activation',
      status: workspace.activationStatus === 'ACTIVE' ? 'healthy' : 'warning',
      message: `Workspace status: ${workspace.activationStatus}`,
      details: { activationStatus: workspace.activationStatus },
    });

    // Check required fields
    if (!workspace.displayName) {
      results.push({
        category: 'Configuration',
        status: 'error',
        message: 'Missing display name',
        fixable: false,
      });
    }

    if (!workspace.subdomain) {
      results.push({
        category: 'Configuration',
        status: 'error',
        message: 'Missing subdomain',
        fixable: false,
      });
    }

    // Check subdomain availability (if not current workspace)
    try {
      const isSubdomainAvailable =
        await this.workspaceService.isSubdomainAvailable(workspace.subdomain);
      if (isSubdomainAvailable) {
        results.push({
          category: 'Configuration',
          status: 'warning',
          message: 'Subdomain appears to be available (potential issue)',
          details: { subdomain: workspace.subdomain },
        });
      } else {
        results.push({
          category: 'Configuration',
          status: 'healthy',
          message: 'Subdomain is properly registered',
        });
      }
    } catch (error) {
      results.push({
        category: 'Configuration',
        status: 'error',
        message: `Failed to check subdomain availability: ${error.message}`,
      });
    }

    return results;
  }

  private async checkDomainHealth(
    workspace: any,
  ): Promise<HealthCheckResult[]> {
    const results: HealthCheckResult[] = [];

    if (workspace.customDomain) {
      try {
        // Check custom domain configuration
        const domainCheck =
          await this.workspaceService.checkCustomDomainValidRecords(workspace);

        if (domainCheck) {
          results.push({
            category: 'Custom Domain',
            status: workspace.isCustomDomainEnabled ? 'healthy' : 'warning',
            message: workspace.isCustomDomainEnabled
              ? 'Custom domain is properly configured and working'
              : 'Custom domain configured but not working',
            details: domainCheck,
            fixable: !workspace.isCustomDomainEnabled,
          });
        } else {
          results.push({
            category: 'Custom Domain',
            status: 'error',
            message: 'Custom domain configuration check failed',
            fixable: true,
          });
        }
      } catch (error) {
        results.push({
          category: 'Custom Domain',
          status: 'error',
          message: `Custom domain validation failed: ${error.message}`,
          fixable: true,
        });
      }
    } else {
      results.push({
        category: 'Custom Domain',
        status: 'healthy',
        message: 'No custom domain configured',
      });
    }

    return results;
  }

  private async checkMetadataHealth(
    workspace: any,
  ): Promise<HealthCheckResult[]> {
    const results: HealthCheckResult[] = [];

    // Check metadata version
    if (workspace.metadataVersion) {
      results.push({
        category: 'Metadata',
        status: 'healthy',
        message: `Metadata version: ${workspace.metadataVersion}`,
        details: { metadataVersion: workspace.metadataVersion },
      });
    } else {
      results.push({
        category: 'Metadata',
        status: 'warning',
        message: 'Metadata version not set',
        fixable: true,
      });
    }

    // Check application version
    if (workspace.version) {
      results.push({
        category: 'Version',
        status: 'healthy',
        message: `Application version: ${workspace.version}`,
      });
    } else {
      results.push({
        category: 'Version',
        status: 'warning',
        message: 'Application version not set',
        fixable: true,
      });
    }

    return results;
  }

  private async checkUserHealth(workspace: any): Promise<HealthCheckResult[]> {
    const results: HealthCheckResult[] = [];

    // This would require access to UserWorkspaceService
    // For now, just check if workspace has proper role configuration
    if (workspace.defaultRoleId) {
      results.push({
        category: 'User Management',
        status: 'healthy',
        message: 'Default role is configured',
        details: { defaultRoleId: workspace.defaultRoleId },
      });
    } else {
      results.push({
        category: 'User Management',
        status: 'error',
        message: 'No default role configured',
        fixable: false,
      });
    }

    return results;
  }

  private displayHealthResults(results: HealthCheckResult[]): void {
    this.commandLogger.info('Health Check Results:');
    console.log();

    results.forEach((result) => {
      const statusIcon = {
        healthy: chalk.green('✓'),
        warning: chalk.yellow('⚠'),
        error: chalk.red('✗'),
      }[result.status];

      const statusColor = {
        healthy: chalk.green,
        warning: chalk.yellow,
        error: chalk.red,
      }[result.status];

      console.log(
        `${statusIcon} ${chalk.bold(result.category)}: ${statusColor(result.message)}`,
      );

      if (result.details) {
        console.log(`  ${chalk.gray(JSON.stringify(result.details, null, 2))}`);
      }

      if (result.fixable) {
        console.log(`  ${chalk.blue('ℹ Can be fixed with --fix option')}`);
      }
    });

    console.log();
  }

  private showFixableIssues(results: HealthCheckResult[]): void {
    const fixableIssues = results.filter((r) => r.fixable);

    if (fixableIssues.length === 0) {
      this.commandLogger.info('No fixable issues found');
      return;
    }

    fixableIssues.forEach((issue) => {
      this.commandLogger.info(
        `Would fix: ${issue.category} - ${issue.message}`,
      );
    });
  }

  private async applyFixes(
    workspace: any,
    results: HealthCheckResult[],
  ): Promise<void> {
    const fixableIssues = results.filter((r) => r.fixable);

    if (fixableIssues.length === 0) {
      this.commandLogger.info('No fixable issues to apply');
      return;
    }

    this.commandLogger.info(`Applying ${fixableIssues.length} fix(es)...`);

    for (const issue of fixableIssues) {
      try {
        await this.applySpecificFix(workspace, issue);
        this.commandLogger.success(
          `Fixed: ${issue.category} - ${issue.message}`,
        );
      } catch (error) {
        this.commandLogger.error(
          `Failed to fix ${issue.category}: ${error.message}`,
        );
      }
    }
  }

  private async applySpecificFix(
    workspace: any,
    issue: HealthCheckResult,
  ): Promise<void> {
    // Implement specific fixes based on issue category and type
    switch (issue.category) {
      case 'Custom Domain':
        // Re-validate custom domain
        await this.workspaceService.checkCustomDomainValidRecords(workspace);
        break;

      case 'Metadata':
        // Could update metadata version if needed
        break;

      default:
        throw new Error(`No fix available for ${issue.category}`);
    }
  }

  private generateSummary(results: HealthCheckResult[]): any {
    return {
      total: results.length,
      healthy: results.filter((r) => r.status === 'healthy').length,
      warnings: results.filter((r) => r.status === 'warning').length,
      errors: results.filter((r) => r.status === 'error').length,
      fixable: results.filter((r) => r.fixable).length,
    };
  }

  private displaySummary(summary: any): void {
    this.commandLogger.info('Health Check Summary:');
    console.log(`  Total checks: ${summary.total}`);
    console.log(`  ${chalk.green('Healthy')}: ${summary.healthy}`);
    console.log(`  ${chalk.yellow('Warnings')}: ${summary.warnings}`);
    console.log(`  ${chalk.red('Errors')}: ${summary.errors}`);
    console.log(`  ${chalk.blue('Fixable issues')}: ${summary.fixable}`);

    if (summary.errors > 0) {
      this.commandLogger.error(
        'Workspace has health issues that require attention',
      );
    } else if (summary.warnings > 0) {
      this.commandLogger.warn(
        'Workspace has some warnings but is generally healthy',
      );
    } else {
      this.commandLogger.success('Workspace is completely healthy!');
    }
  }

  @Option({
    flags: '-w, --workspace-id <workspaceId>',
    description: 'Workspace ID to check',
    required: true,
  })
  parseWorkspaceId(value: string): string {
    if (!value || value.trim().length === 0) {
      throw new Error('Workspace ID is required');
    }
    return value.trim();
  }

  @Option({
    flags: '--fix',
    description: 'Attempt to fix detected issues',
  })
  parseFix(): boolean {
    return true;
  }

  @Option({
    flags: '--dry-run',
    description: 'Show what would be fixed without applying changes',
  })
  parseDryRun(): boolean {
    return true;
  }

  @Option({
    flags: '--no-check-domain',
    description: 'Skip domain health checks',
  })
  parseNoDomain(): boolean {
    return false;
  }

  @Option({
    flags: '--no-check-metadata',
    description: 'Skip metadata health checks',
  })
  parseNoMetadata(): boolean {
    return false;
  }

  @Option({
    flags: '--no-check-users',
    description: 'Skip user/membership health checks',
  })
  parseNoUsers(): boolean {
    return false;
  }
}
