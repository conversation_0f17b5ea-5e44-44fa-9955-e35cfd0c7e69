import { Injectable, Logger } from '@nestjs/common';
import chalk from 'chalk';
import { plainToClass } from 'class-transformer';
import { validate } from 'class-validator';
import { Command, CommandRunner, Option } from 'nest-commander';
import { UpdateWorkspaceInput } from 'twenty-server/engine/core-modules/workspace/dtos/update-workspace-input';
import { WorkspaceService } from 'twenty-server/engine/core-modules/workspace/services/workspace.service';
import { workspaceValidator } from 'twenty-server/engine/core-modules/workspace/workspace.validate';
import { CommandLogger } from '../utils/command-logger';

interface WorkspaceUpdateCommandOptions {
  workspaceId: string;
  displayName?: string;
  subdomain?: string;
  customDomain?: string;
  allowImpersonation?: boolean;
  isPublicInviteLinkEnabled?: boolean;
  isGoogleAuthEnabled?: boolean;
  isMicrosoftAuthEnabled?: boolean;
  isPasswordAuthEnabled?: boolean;
  dryRun?: boolean;
}

@Injectable()
@Command({
  name: 'workspace:update',
  description: 'Update workspace properties with validation and safety checks',
  aliases: ['ws:update', 'update'],
})
export class WorkspaceUpdateCommand extends CommandRunner {
  private readonly logger = new Logger(WorkspaceUpdateCommand.name);

  constructor(
    private readonly workspaceService: WorkspaceService,
    private readonly commandLogger: CommandLogger,
  ) {
    super();
  }

  async run(
    _passedParam: string[],
    options: WorkspaceUpdateCommandOptions,
  ): Promise<void> {
    try {
      this.commandLogger.info(`Updating workspace: ${options.workspaceId}`);

      // Validate workspace exists
      const workspace = await this.workspaceService.findById(
        options.workspaceId,
      );
      workspaceValidator.assertIsDefinedOrThrow(workspace);

      this.commandLogger.success(
        `Found workspace: ${workspace.displayName} (${workspace.subdomain})`,
      );

      // Build update payload
      const updatePayload = this.buildUpdatePayload(options);

      if (Object.keys(updatePayload).length === 0) {
        this.commandLogger.warn('No update fields provided');
        return;
      }

      // Validate update payload using existing DTO
      await this.validateUpdatePayload(updatePayload);

      // Show what will be updated
      this.showUpdateSummary(workspace, updatePayload, options.dryRun || false);

      if (options.dryRun) {
        this.commandLogger.info('Dry run mode: No changes will be applied');
        return;
      }

      // Apply update using existing service
      const updatedWorkspace = await this.workspaceService.updateWorkspaceById({
        payload: {
          id: options.workspaceId,
          ...updatePayload,
        },
        // Note: In real implementation, you'd need proper auth context
        // For CLI, this might use a special admin context or service account
      });

      this.commandLogger.success('Workspace updated successfully!');
      this.displayUpdatedWorkspace(updatedWorkspace);
    } catch (error) {
      this.commandLogger.error(
        'Failed to update workspace',
        error instanceof Error ? error.message : String(error),
      );
      throw error;
    }
  }

  private buildUpdatePayload(
    options: WorkspaceUpdateCommandOptions,
  ): Partial<UpdateWorkspaceInput> {
    const payload: Partial<UpdateWorkspaceInput> = {};

    if (options.displayName !== undefined) {
      payload.displayName = options.displayName;
    }

    if (options.subdomain !== undefined) {
      payload.subdomain = options.subdomain;
    }

    if (options.customDomain !== undefined) {
      payload.customDomain =
        options.customDomain === 'null' ? undefined : options.customDomain;
    }

    if (options.allowImpersonation !== undefined) {
      payload.allowImpersonation = options.allowImpersonation;
    }

    if (options.isPublicInviteLinkEnabled !== undefined) {
      payload.isPublicInviteLinkEnabled = options.isPublicInviteLinkEnabled;
    }

    if (options.isGoogleAuthEnabled !== undefined) {
      payload.isGoogleAuthEnabled = options.isGoogleAuthEnabled;
    }

    if (options.isMicrosoftAuthEnabled !== undefined) {
      payload.isMicrosoftAuthEnabled = options.isMicrosoftAuthEnabled;
    }

    if (options.isPasswordAuthEnabled !== undefined) {
      payload.isPasswordAuthEnabled = options.isPasswordAuthEnabled;
    }

    return payload;
  }

  private async validateUpdatePayload(
    payload: Partial<UpdateWorkspaceInput>,
  ): Promise<void> {
    // Use existing validation from UpdateWorkspaceInput DTO
    const updateInput = plainToClass(UpdateWorkspaceInput, payload);
    const errors = await validate(updateInput, { skipMissingProperties: true });

    if (errors.length > 0) {
      const errorMessages = errors
        .map((error) => Object.values(error.constraints || {}).join(', '))
        .join('; ');

      throw new Error(`Validation failed: ${errorMessages}`);
    }

    this.commandLogger.success('Validation passed');
  }

  private showUpdateSummary(
    currentWorkspace: any,
    updatePayload: any,
    isDryRun: boolean,
  ): void {
    this.commandLogger.info(
      isDryRun ? 'Changes to be applied:' : 'Applying changes:',
    );
    console.log();

    Object.entries(updatePayload).forEach(([key, newValue]) => {
      const currentValue = currentWorkspace[key];
      const hasChanged = currentValue !== newValue;

      if (hasChanged) {
        console.log(`  ${chalk.bold(key)}:`);
        console.log(`    ${chalk.red('From')}: ${currentValue || 'null'}`);
        console.log(`    ${chalk.green('To')}: ${newValue || 'null'}`);
      } else {
        console.log(
          `  ${chalk.bold(key)}: ${chalk.gray('No change')} (${currentValue})`,
        );
      }
    });

    console.log();
  }

  private displayUpdatedWorkspace(workspace: any): void {
    this.commandLogger.info('Updated workspace details:');

    const details = {
      ID: workspace.id,
      'Display Name': workspace.displayName,
      Subdomain: workspace.subdomain,
      'Custom Domain': workspace.customDomain || 'None',
      Status: workspace.activationStatus,
      'Google Auth': workspace.isGoogleAuthEnabled ? 'Enabled' : 'Disabled',
      'Microsoft Auth': workspace.isMicrosoftAuthEnabled
        ? 'Enabled'
        : 'Disabled',
      'Password Auth': workspace.isPasswordAuthEnabled ? 'Enabled' : 'Disabled',
      'Public Invite': workspace.isPublicInviteLinkEnabled
        ? 'Enabled'
        : 'Disabled',
      'Allow Impersonation': workspace.allowImpersonation
        ? 'Enabled'
        : 'Disabled',
      'Updated At': workspace.updatedAt,
    };

    console.table(details);
  }

  @Option({
    flags: '-w, --workspace-id <workspaceId>',
    description: 'Workspace ID to update',
    required: true,
  })
  parseWorkspaceId(value: string): string {
    if (!value || value.trim().length === 0) {
      throw new Error('Workspace ID is required');
    }
    return value.trim();
  }

  @Option({
    flags: '-n, --display-name <displayName>',
    description: 'Update workspace display name',
  })
  parseDisplayName(value: string): string {
    return value;
  }

  @Option({
    flags: '-s, --subdomain <subdomain>',
    description: 'Update workspace subdomain',
  })
  parseSubdomain(value: string): string {
    // Basic validation - full validation happens in DTO
    if (value.includes(' ')) {
      throw new Error('Subdomain cannot contain spaces');
    }
    return value;
  }

  @Option({
    flags: '-d, --custom-domain <customDomain>',
    description: 'Update custom domain (use "null" to remove)',
  })
  parseCustomDomain(value: string): string {
    return value;
  }

  @Option({
    flags: '--allow-impersonation <boolean>',
    description: 'Enable/disable impersonation (true/false)',
  })
  parseAllowImpersonation(value: string): boolean {
    if (!['true', 'false'].includes(value.toLowerCase())) {
      throw new Error('Allow impersonation must be "true" or "false"');
    }
    return value.toLowerCase() === 'true';
  }

  @Option({
    flags: '--public-invite <boolean>',
    description: 'Enable/disable public invite link (true/false)',
    // The property name must match the expected field in buildUpdatePayload
    // so we use 'isPublicInviteLinkEnabled' here.
    name: 'isPublicInviteLinkEnabled',
  })
  parseIsPublicInviteLinkEnabled(value: string): boolean {
    if (!['true', 'false'].includes(value.toLowerCase())) {
      throw new Error('Public invite must be "true" or "false"');
    }
    return value.toLowerCase() === 'true';
  }

  @Option({
    flags: '--google-auth <boolean>',
    description: 'Enable/disable Google authentication (true/false)',
  })
  parseGoogleAuth(value: string): boolean {
    if (!['true', 'false'].includes(value.toLowerCase())) {
      throw new Error('Google auth must be "true" or "false"');
    }
    return value.toLowerCase() === 'true';
  }

  @Option({
    flags: '--microsoft-auth <boolean>',
    description: 'Enable/disable Microsoft authentication (true/false)',
  })
  parseMicrosoftAuth(value: string): boolean {
    if (!['true', 'false'].includes(value.toLowerCase())) {
      throw new Error('Microsoft auth must be "true" or "false"');
    }
    return value.toLowerCase() === 'true';
  }

  @Option({
    flags: '--password-auth <boolean>',
    description: 'Enable/disable password authentication (true/false)',
  })
  parsePasswordAuth(value: string): boolean {
    if (!['true', 'false'].includes(value.toLowerCase())) {
      throw new Error('Password auth must be "true" or "false"');
    }
    return value.toLowerCase() === 'true';
  }

  @Option({
    flags: '--dry-run',
    description: 'Show what would be updated without applying changes',
  })
  parseDryRun(): boolean {
    return true;
  }
}
