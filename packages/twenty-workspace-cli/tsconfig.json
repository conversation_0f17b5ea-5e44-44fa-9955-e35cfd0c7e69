{"extends": "../../tsconfig.base.json", "compilerOptions": {"outDir": "./dist", "rootDir": ".", "target": "es2020", "module": "commonjs", "lib": ["es2020"], "declaration": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "moduleResolution": "node", "baseUrl": "./", "paths": {"src/*": ["./src/*"], "twenty-server/*": ["../twenty-server/src/*"]}, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true}, "include": ["src/**/*", "../twenty-server/src/**/*.ts"], "exclude": ["node_modules", "dist", "**/*.spec.ts", "**/*.test.ts"]}